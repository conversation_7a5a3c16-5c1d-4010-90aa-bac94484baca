{"name": "whatsapp-ai-agent", "version": "1.0.0", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["whatsapp", "ai", "agent", "chatbot", "automation"], "author": "", "license": "ISC", "description": "WhatsApp AI Agent for shopping, assignments, YouTube, flights, and hotels", "dependencies": {"axios": "^1.6.2", "cheerio": "^1.0.0", "dotenv": "^16.3.1", "express": "^4.18.2", "moment": "^2.30.1", "node-cron": "^3.0.3", "openai": "^4.20.1", "pdf-lib": "^1.17.1", "playwright-chromium": "^1.52.0", "puppeteer": "^21.5.2", "qrcode-terminal": "^0.12.0", "whatsapp-web.js": "^1.23.0", "winston": "^3.11.0", "ytdl-core": "^4.11.5"}, "devDependencies": {"nodemon": "^3.0.2"}}