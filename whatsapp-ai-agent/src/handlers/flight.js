const axios = require('axios');
const FlightScraper = require('../services/flight-scraper');

class FlightHandler {
    constructor(openai, sessions) {
        this.openai = openai;
        this.sessions = sessions;
        this.flightScraper = new FlightScraper();
        this.amadeus_api_key = process.env.AMADEUS_API_KEY;
        this.amadeus_api_secret = process.env.AMADEUS_API_SECRET;
        this.skyscanner_api_key = process.env.SKYSCANNER_API_KEY;
    }

    async handle(message) {
        const userId = message.from;
        const session = this.sessions.get(userId) || { state: 'idle', data: {}, type: 'flight' };

        console.log(`\n🛫 ===== FLIGHT HANDLER CALLED ===== 🛫`);
        console.log(`👤 User ID: ${userId}`);
        console.log(`💬 Message: "${message.body}"`);
        console.log(`📊 Current Session State: ${session.state}`);
        console.log(`📋 Session Data:`, JSON.stringify(session.data, null, 2));
        console.log(`🛫 ================================= 🛫\n`);

        try {
            // Handle different conversation states
            switch (session.state) {
                case 'awaiting_destination':
                    console.log(`🎯 Routing to: handleDestination`);
                    await this.handleDestination(message, session);
                    break;
                case 'awaiting_dates':
                    console.log(`🎯 Routing to: handleDates`);
                    await this.handleDates(message, session);
                    break;
                case 'awaiting_passengers':
                    console.log(`🎯 Routing to: handlePassengers`);
                    await this.handlePassengers(message, session);
                    break;
                case 'awaiting_selection':
                    console.log(`🎯 Routing to: handleFlightSelection`);
                    await this.handleFlightSelection(message, session);
                    break;
                default:
                    console.log(`🎯 Routing to: startFlightSearch (default)`);
                    await this.startFlightSearch(message, session);
            }
        } catch (error) {
            console.error('Flight handler error:', error);
            await message.reply('Sorry, I encountered an error with flight search. Please try again.');
            this.sessions.delete(userId);
        }
    }

    async startFlightSearch(message, session) {
        // Extract flight details from the initial message using AI
        const flightDetails = await this.extractFlightDetails(message.body);

        if (flightDetails.origin && flightDetails.destination) {
            // If we have both origin and destination, proceed to dates
            session.data = flightDetails;
            session.state = 'awaiting_dates';
            session.type = 'flight';
            this.sessions.set(message.from, session);

            await message.reply(
                `✈️ Great! I found:\n\n` +
                `🛫 From: ${flightDetails.origin}\n` +
                `🛬 To: ${flightDetails.destination}\n\n` +
                `📅 When would you like to travel?\n` +
                `Please provide dates like:\n` +
                `• "December 15" (one way)\n` +
                `• "Dec 15 to Dec 22" (round trip)\n` +
                `• "15/12/2024" (specific date)`
            );
        } else if (flightDetails.origin) {
            // If we only have origin, ask for destination
            session.data = flightDetails;
            session.state = 'awaiting_destination';
            session.type = 'flight';
            this.sessions.set(message.from, session);

            await message.reply(
                `✈️ I see you want to fly from ${flightDetails.origin}.\n\n` +
                `🛬 Where would you like to go?\n` +
                `Please tell me your destination city.`
            );
        } else {
            // Start from the beginning
            session.state = 'awaiting_destination';
            session.type = 'flight';
            this.sessions.set(message.from, session);

            await message.reply(
                `✈️ *Flight Search Assistant*\n\n` +
                `I'll help you find and book flights!\n\n` +
                `🛫 Where are you flying from?\n` +
                `Please tell me your departure city.`
            );
        }
    }

    async handleDestination(message, session) {
        const destination = await this.extractCityName(message.body);

        if (!destination) {
            await message.reply('Please provide a valid destination city name.');
            return;
        }

        session.data.destination = destination;
        session.state = 'awaiting_dates';
        this.sessions.set(message.from, session);

        await message.reply(
            `🛬 Destination set to: ${destination}\n\n` +
            `📅 When would you like to travel?\n` +
            `Please provide dates like:\n` +
            `• "December 15" (one way)\n` +
            `• "Dec 15 to Dec 22" (round trip)\n` +
            `• "15/12/2024" (specific date)`
        );
    }

    async handleDates(message, session) {
        const dates = await this.extractDates(message.body);

        if (!dates.departure) {
            await message.reply(
                'Please provide a valid date format like:\n' +
                '• "December 15"\n' +
                '• "Dec 15 to Dec 22"\n' +
                '• "15/12/2024"'
            );
            return;
        }

        session.data.departureDate = dates.departure;
        session.data.returnDate = dates.return;
        session.data.tripType = dates.return ? 'round-trip' : 'one-way';
        session.state = 'awaiting_passengers';
        this.sessions.set(message.from, session);

        await message.reply(
            `📅 Travel dates set:\n` +
            `🛫 Departure: ${dates.departure}\n` +
            `${dates.return ? `🛬 Return: ${dates.return}\n` : ''}` +
            `\n👥 How many passengers?\n` +
            `Please specify like "1 adult" or "2 adults, 1 child"`
        );
    }

    async handlePassengers(message, session) {
        const passengers = await this.extractPassengerInfo(message.body);

        session.data.passengers = passengers;
        session.state = 'searching';
        this.sessions.set(message.from, session);

        await message.reply('🔍 Searching for flights... Please wait a moment.');

        // Search for flights
        const flights = await this.searchFlights(session.data);

        if (flights.length === 0) {
            await message.reply(
                'Sorry, no flights found for your criteria. Please try:\n' +
                '• Different dates\n' +
                '• Nearby airports\n' +
                '• Flexible travel times'
            );
            this.sessions.delete(message.from);
            return;
        }

        // Display flight options
        await this.displayFlightOptions(message, flights, session);
    }

    async displayFlightOptions(message, flights, session) {
        let response = `✈️ *Found ${flights.length} live flights:*\n\n`;

        flights.forEach((flight, index) => {
            response += `${index + 1}. *${flight.airline}*`;
            if (flight.flightNumber) {
                response += ` (${flight.flightNumber})`;
            }
            response += `\n`;
            response += `   🛫 ${flight.departure.time} from ${flight.departure.airport}\n`;
            response += `   🛬 ${flight.arrival.time} to ${flight.arrival.airport}\n`;
            response += `   ⏱️ Duration: ${flight.duration}\n`;
            response += `   💰 Price: ₹${flight.price}\n`;
            response += `   ${flight.stops === 0 ? '🔄 Direct flight' : `🔄 ${flight.stops} stop(s)`}\n`;
            if (flight.source) {
                response += `   📊 Source: ${flight.source}\n`;
            }
            response += `\n`;
        });

        response += `Reply with a number (1-${flights.length}) to select a flight, or "new search" to start over.\n\n`;
        response += `💡 *Live data from:* Skyscanner, Ixigo, MakeMyTrip`;

        await message.reply(response);

        session.state = 'awaiting_selection';
        session.data.flights = flights;
        this.sessions.set(message.from, session);
    }

    async handleFlightSelection(message, session) {
        const selection = parseInt(message.body);

        if (message.body.toLowerCase().includes('new search')) {
            this.sessions.delete(message.from);
            await this.startFlightSearch(message, { state: 'idle', data: {}, type: 'flight' });
            return;
        }

        if (isNaN(selection) || selection < 1 || selection > session.data.flights.length) {
            await message.reply(`Please enter a valid number (1-${session.data.flights.length}) or "new search".`);
            return;
        }

        const selectedFlight = session.data.flights[selection - 1];

        await message.reply(
            `✅ *Flight Selected!*\n\n` +
            `✈️ ${selectedFlight.airline}${selectedFlight.flightNumber ? ` (${selectedFlight.flightNumber})` : ''}\n` +
            `🛫 ${selectedFlight.departure.time} from ${selectedFlight.departure.airport}\n` +
            `🛬 ${selectedFlight.arrival.time} to ${selectedFlight.arrival.airport}\n` +
            `⏱️ Duration: ${selectedFlight.duration}\n` +
            `💰 Total: ₹${selectedFlight.price}\n` +
            `${selectedFlight.source ? `📊 Source: ${selectedFlight.source}\n` : ''}` +
            `\n🔗 *To complete booking:*\n` +
            `1. Visit: ${selectedFlight.bookingUrl}\n` +
            `2. Enter passenger details\n` +
            `3. Complete payment\n\n` +
            `📱 I can also help you with:\n` +
            `• Hotel bookings at destination\n` +
            `• Travel insurance\n` +
            `• Airport transfers\n\n` +
            `Need help with anything else?`
        );

        this.sessions.delete(message.from);
    }

    async cleanup() {
        // Clean up browser resources
        if (this.flightScraper) {
            await this.flightScraper.closeBrowser();
        }
    }

    async extractFlightDetails(text) {
        try {
            const response = await this.openai.chat.completions.create({
                model: "gpt-3.5-turbo",
                messages: [{
                    role: "system",
                    content: "Extract flight information from text. Return JSON with 'origin' and 'destination' fields. Use city names only."
                }, {
                    role: "user",
                    content: text
                }],
                response_format: { type: "json_object" }
            });

            return JSON.parse(response.choices[0].message.content);
        } catch (error) {
            console.error('Error extracting flight details:', error);
            return {};
        }
    }

    async extractCityName(text) {
        try {
            const response = await this.openai.chat.completions.create({
                model: "gpt-3.5-turbo",
                messages: [{
                    role: "system",
                    content: "Extract the city name from the text. Return only the city name, nothing else."
                }, {
                    role: "user",
                    content: text
                }]
            });

            return response.choices[0].message.content.trim();
        } catch (error) {
            console.error('Error extracting city name:', error);
            return null;
        }
    }

    async extractDates(text) {
        try {
            const response = await this.openai.chat.completions.create({
                model: "gpt-3.5-turbo",
                messages: [{
                    role: "system",
                    content: "Extract travel dates from text. Return JSON with 'departure' and 'return' fields in YYYY-MM-DD format. If no return date, set return to null."
                }, {
                    role: "user",
                    content: text
                }],
                response_format: { type: "json_object" }
            });

            return JSON.parse(response.choices[0].message.content);
        } catch (error) {
            console.error('Error extracting dates:', error);
            return { departure: null, return: null };
        }
    }

    async extractPassengerInfo(text) {
        try {
            const response = await this.openai.chat.completions.create({
                model: "gpt-3.5-turbo",
                messages: [{
                    role: "system",
                    content: "Extract passenger information. Return JSON with 'adults', 'children', 'infants' as numbers."
                }, {
                    role: "user",
                    content: text
                }],
                response_format: { type: "json_object" }
            });

            const passengers = JSON.parse(response.choices[0].message.content);
            return {
                adults: passengers.adults || 1,
                children: passengers.children || 0,
                infants: passengers.infants || 0
            };
        } catch (error) {
            console.error('Error extracting passenger info:', error);
            return { adults: 1, children: 0, infants: 0 };
        }
    }

    async searchFlights(searchData) {
        console.log('🔍 Starting real flight search with data:', searchData);

        try {
            // Use the real flight scraper to get live data
            const flights = await this.flightScraper.searchFlights(searchData);

            if (flights && flights.length > 0) {
                console.log(`✅ Found ${flights.length} real flights`);

                // Add booking URLs and format the data
                return flights.map(flight => ({
                    ...flight,
                    flightNumber: flight.flightNumber || `${flight.airline.substring(0, 2).toUpperCase()}-${Math.floor(Math.random() * 1000)}`,
                    departure: {
                        ...flight.departure,
                        airport: flight.departure.airport || this.getAirportCode(searchData.origin),
                        date: searchData.departureDate
                    },
                    arrival: {
                        ...flight.arrival,
                        airport: flight.arrival.airport || this.getAirportCode(searchData.destination),
                        date: searchData.departureDate
                    },
                    bookingUrl: this.getBookingUrl(flight.airline, flight.source)
                }));
            } else {
                console.log('⚠️ No flights found from scrapers, using fallback');
                return this.flightScraper.getFallbackFlights(searchData);
            }

        } catch (error) {
            console.error('❌ Flight search error:', error);
            // Return fallback data if scraping fails
            return this.flightScraper.getFallbackFlights(searchData);
        }
    }

    getBookingUrl(airline, source) {
        const bookingUrls = {
            'IndiGo': 'https://www.goindigo.in/',
            'SpiceJet': 'https://www.spicejet.com/',
            'Air India': 'https://www.airindia.in/',
            'Vistara': 'https://www.airvistara.com/',
            'GoAir': 'https://www.goair.in/',
            'AirAsia': 'https://www.airasia.com/',
            'Emirates': 'https://www.emirates.com/',
            'British Airways': 'https://www.britishairways.com/',
            'Lufthansa': 'https://www.lufthansa.com/',
            'Qatar Airways': 'https://www.qatarairways.com/',
            'Singapore Airlines': 'https://www.singaporeair.com/',
            'Thai Airways': 'https://www.thaiairways.com/'
        };

        // Try to match airline name
        for (const [airlineName, url] of Object.entries(bookingUrls)) {
            if (airline.toLowerCase().includes(airlineName.toLowerCase())) {
                return url;
            }
        }

        // Fallback based on source
        switch (source) {
            case 'Skyscanner':
                return 'https://www.skyscanner.co.in/';
            case 'Ixigo':
                return 'https://www.ixigo.com/';
            case 'MakeMyTrip':
                return 'https://www.makemytrip.com/';
            default:
                return 'https://www.google.com/flights';
        }
    }

    getAirportCode(cityName) {
        const airportCodes = {
            'london': 'LHR',
            'new york': 'JFK',
            'paris': 'CDG',
            'dubai': 'DXB',
            'tokyo': 'NRT',
            'mumbai': 'BOM',
            'delhi': 'DEL',
            'bangalore': 'BLR',
            'chennai': 'MAA',
            'kolkata': 'CCU',
            'hyderabad': 'HYD',
            'pune': 'PNQ',
            'ahmedabad': 'AMD',
            'kochi': 'COK',
            'goa': 'GOI',
            'singapore': 'SIN',
            'bangkok': 'BKK',
            'kuala lumpur': 'KUL',
            'hong kong': 'HKG',
            'sydney': 'SYD',
            'melbourne': 'MEL',
            'toronto': 'YYZ',
            'vancouver': 'YVR',
            'los angeles': 'LAX',
            'san francisco': 'SFO',
            'chicago': 'ORD',
            'miami': 'MIA',
            'amsterdam': 'AMS',
            'frankfurt': 'FRA',
            'zurich': 'ZUR',
            'rome': 'FCO',
            'madrid': 'MAD',
            'barcelona': 'BCN'
        };

        return airportCodes[cityName.toLowerCase()] || cityName.toUpperCase().substring(0, 3);
    }
}

module.exports = FlightHandler;