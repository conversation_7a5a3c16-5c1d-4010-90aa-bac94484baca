const puppeteer = require('puppeteer');
const axios = require('axios');
const cheerio = require('cheerio');
const moment = require('moment');

class FlightScraper {
    constructor() {
        this.browser = null;
        this.userAgents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        ];
    }

    async initBrowser() {
        if (!this.browser) {
            this.browser = await puppeteer.launch({
                headless: true,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--single-process',
                    '--disable-gpu'
                ]
            });
        }
        return this.browser;
    }

    async closeBrowser() {
        if (this.browser) {
            await this.browser.close();
            this.browser = null;
        }
    }

    getRandomUserAgent() {
        return this.userAgents[Math.floor(Math.random() * this.userAgents.length)];
    }

    formatDate(dateString) {
        return moment(dateString).format('YYYY-MM-DD');
    }

    getAirportCode(cityName) {
        const airportCodes = {
            'mumbai': 'BOM',
            'delhi': 'DEL',
            'bangalore': 'BLR',
            'chennai': 'MAA',
            'kolkata': 'CCU',
            'hyderabad': 'HYD',
            'pune': 'PNQ',
            'ahmedabad': 'AMD',
            'kochi': 'COK',
            'goa': 'GOI',
            'london': 'LHR',
            'new york': 'JFK',
            'nyc': 'JFK',
            'paris': 'CDG',
            'dubai': 'DXB',
            'tokyo': 'NRT',
            'singapore': 'SIN',
            'bangkok': 'BKK',
            'kuala lumpur': 'KUL',
            'hong kong': 'HKG',
            'sydney': 'SYD',
            'melbourne': 'MEL',
            'toronto': 'YYZ',
            'vancouver': 'YVR',
            'los angeles': 'LAX',
            'san francisco': 'SFO',
            'chicago': 'ORD',
            'miami': 'MIA',
            'amsterdam': 'AMS',
            'frankfurt': 'FRA',
            'zurich': 'ZUR',
            'rome': 'FCO',
            'madrid': 'MAD',
            'barcelona': 'BCN'
        };
        return airportCodes[cityName.toLowerCase()] || cityName.toUpperCase().substring(0, 3);
    }

    async searchFlights(searchData) {
        console.log('🔍 Starting real flight search with data:', searchData);

        const results = [];

        try {
            // Run multiple scrapers in parallel
            const scrapers = [
                this.scrapeSkyscanner(searchData),
                this.scrapeIxigo(searchData),
                this.scrapeMakeMyTrip(searchData)
            ];

            const scraperResults = await Promise.allSettled(scrapers);

            scraperResults.forEach((result, index) => {
                const scraperNames = ['Skyscanner', 'Ixigo', 'MakeMyTrip'];
                if (result.status === 'fulfilled' && result.value.length > 0) {
                    console.log(`✅ ${scraperNames[index]} found ${result.value.length} flights`);
                    results.push(...result.value);
                } else {
                    console.log(`❌ ${scraperNames[index]} failed:`, result.reason?.message || 'Unknown error');
                }
            });

            // Deduplicate and sort results
            const uniqueFlights = this.deduplicateFlights(results);
            const sortedFlights = uniqueFlights.sort((a, b) => a.price - b.price);

            console.log(`🎯 Total unique flights found: ${sortedFlights.length}`);
            return sortedFlights.slice(0, 5); // Return top 5 flights

        } catch (error) {
            console.error('❌ Flight search error:', error);
            // Return fallback mock data if all scrapers fail
            return this.getFallbackFlights(searchData);
        }
    }

    async scrapeSkyscanner(searchData) {
        console.log('🔍 Scraping Skyscanner...');

        try {
            const browser = await this.initBrowser();
            const page = await browser.newPage();

            await page.setUserAgent(this.getRandomUserAgent());
            await page.setViewport({ width: 1366, height: 768 });

            const originCode = this.getAirportCode(searchData.origin);
            const destCode = this.getAirportCode(searchData.destination);
            const departDate = this.formatDate(searchData.departureDate);

            const url = `https://www.skyscanner.co.in/transport/flights/${originCode}/${destCode}/${departDate.replace(/-/g, '')}/?adults=${searchData.passengers?.adults || 1}&children=${searchData.passengers?.children || 0}&adultsv2=${searchData.passengers?.adults || 1}&childrenv2=${searchData.passengers?.children || 0}&infants=${searchData.passengers?.infants || 0}&cabinclass=economy&rtn=0&preferdirects=false&outboundaltsenabled=false&inboundaltsenabled=false`;

            console.log('🌐 Skyscanner URL:', url);

            await page.goto(url, { waitUntil: 'networkidle2', timeout: 30000 });

            // Wait for flights to load
            await page.waitForTimeout(5000);

            // Try to close any popups
            try {
                await page.click('[data-testid="dialog-close-button"]', { timeout: 2000 });
            } catch (e) {
                // Popup might not exist
            }

            // Wait for flight results
            await page.waitForSelector('[data-testid="result"]', { timeout: 15000 });

            const flights = await page.evaluate(() => {
                const flightElements = document.querySelectorAll('[data-testid="result"]');
                const results = [];

                flightElements.forEach((element, index) => {
                    if (index >= 5) return; // Limit to 5 flights

                    try {
                        const priceElement = element.querySelector('[data-testid="price"]');
                        const timeElements = element.querySelectorAll('[data-testid="time"]');
                        const airlineElement = element.querySelector('[data-testid="airline-name"]');
                        const durationElement = element.querySelector('[data-testid="duration"]');
                        const stopsElement = element.querySelector('[data-testid="stops"]');

                        if (priceElement && timeElements.length >= 2) {
                            const price = parseInt(priceElement.textContent.replace(/[^\d]/g, ''));
                            const departureTime = timeElements[0]?.textContent?.trim();
                            const arrivalTime = timeElements[1]?.textContent?.trim();
                            const airline = airlineElement?.textContent?.trim() || 'Unknown Airline';
                            const duration = durationElement?.textContent?.trim() || 'N/A';
                            const stopsText = stopsElement?.textContent?.trim() || '0 stops';
                            const stops = stopsText.includes('Direct') ? 0 : parseInt(stopsText) || 0;

                            results.push({
                                airline,
                                price,
                                departure: { time: departureTime },
                                arrival: { time: arrivalTime },
                                duration,
                                stops,
                                source: 'Skyscanner'
                            });
                        }
                    } catch (e) {
                        console.log('Error parsing flight element:', e);
                    }
                });

                return results;
            });

            await page.close();
            console.log(`✅ Skyscanner found ${flights.length} flights`);
            return flights;

        } catch (error) {
            console.error('❌ Skyscanner scraping failed:', error.message);
            return [];
        }
    }

    async scrapeIxigo(searchData) {
        console.log('🔍 Scraping Ixigo...');

        try {
            const browser = await this.initBrowser();
            const page = await browser.newPage();

            await page.setUserAgent(this.getRandomUserAgent());

            const originCode = this.getAirportCode(searchData.origin);
            const destCode = this.getAirportCode(searchData.destination);
            const departDate = moment(searchData.departureDate).format('DD-MM-YYYY');

            const url = `https://www.ixigo.com/search/result/flight/${originCode}-${destCode}/${departDate}?adults=${searchData.passengers?.adults || 1}&children=${searchData.passengers?.children || 0}&infants=${searchData.passengers?.infants || 0}&class=E`;

            console.log('🌐 Ixigo URL:', url);

            await page.goto(url, { waitUntil: 'networkidle2', timeout: 30000 });
            await page.waitForTimeout(8000);

            const flights = await page.evaluate(() => {
                const flightCards = document.querySelectorAll('.flight-card, .ixi-flight-card');
                const results = [];

                flightCards.forEach((card, index) => {
                    if (index >= 5) return;

                    try {
                        const priceElement = card.querySelector('.price, .fare-price, .flight-price');
                        const airlineElement = card.querySelector('.airline-name, .carrier-name');
                        const timeElements = card.querySelectorAll('.time, .flight-time');
                        const durationElement = card.querySelector('.duration, .flight-duration');

                        if (priceElement) {
                            const price = parseInt(priceElement.textContent.replace(/[^\d]/g, ''));
                            const airline = airlineElement?.textContent?.trim() || 'Unknown Airline';
                            const departureTime = timeElements[0]?.textContent?.trim() || 'N/A';
                            const arrivalTime = timeElements[1]?.textContent?.trim() || 'N/A';
                            const duration = durationElement?.textContent?.trim() || 'N/A';

                            results.push({
                                airline,
                                price,
                                departure: { time: departureTime },
                                arrival: { time: arrivalTime },
                                duration,
                                stops: 0, // Default to direct
                                source: 'Ixigo'
                            });
                        }
                    } catch (e) {
                        console.log('Error parsing Ixigo flight:', e);
                    }
                });

                return results;
            });

            await page.close();
            console.log(`✅ Ixigo found ${flights.length} flights`);
            return flights;

        } catch (error) {
            console.error('❌ Ixigo scraping failed:', error.message);
            return [];
        }
    }

    async scrapeMakeMyTrip(searchData) {
        console.log('🔍 Scraping MakeMyTrip...');

        try {
            const browser = await this.initBrowser();
            const page = await browser.newPage();

            await page.setUserAgent(this.getRandomUserAgent());

            const originCode = this.getAirportCode(searchData.origin);
            const destCode = this.getAirportCode(searchData.destination);
            const departDate = moment(searchData.departureDate).format('DD-MM-YYYY');

            const url = `https://www.makemytrip.com/flight/search?tripType=O&from=${originCode}&to=${destCode}&departure=${departDate}&paxType=A-${searchData.passengers?.adults || 1}_C-${searchData.passengers?.children || 0}_I-${searchData.passengers?.infants || 0}&intl=false&cabinClass=E`;

            console.log('🌐 MakeMyTrip URL:', url);

            await page.goto(url, { waitUntil: 'networkidle2', timeout: 30000 });
            await page.waitForTimeout(10000);

            // Handle potential popups
            try {
                await page.click('.commonModal__close', { timeout: 3000 });
            } catch (e) {
                // Popup might not exist
            }

            const flights = await page.evaluate(() => {
                const flightCards = document.querySelectorAll('.listingCard, .flight-card');
                const results = [];

                flightCards.forEach((card, index) => {
                    if (index >= 5) return;

                    try {
                        const priceElement = card.querySelector('.actualPrice, .price');
                        const airlineElement = card.querySelector('.airlineName, .airline-name');
                        const timeElements = card.querySelectorAll('.dept-time, .arr-time, .time');
                        const durationElement = card.querySelector('.duration');
                        const stopsElement = card.querySelector('.stops-info, .stops');

                        if (priceElement) {
                            const price = parseInt(priceElement.textContent.replace(/[^\d]/g, ''));
                            const airline = airlineElement?.textContent?.trim() || 'Unknown Airline';
                            const departureTime = timeElements[0]?.textContent?.trim() || 'N/A';
                            const arrivalTime = timeElements[1]?.textContent?.trim() || 'N/A';
                            const duration = durationElement?.textContent?.trim() || 'N/A';
                            const stopsText = stopsElement?.textContent?.trim() || '0 stops';
                            const stops = stopsText.includes('non-stop') ? 0 : parseInt(stopsText) || 0;

                            results.push({
                                airline,
                                price,
                                departure: { time: departureTime },
                                arrival: { time: arrivalTime },
                                duration,
                                stops,
                                source: 'MakeMyTrip'
                            });
                        }
                    } catch (e) {
                        console.log('Error parsing MakeMyTrip flight:', e);
                    }
                });

                return results;
            });

            await page.close();
            console.log(`✅ MakeMyTrip found ${flights.length} flights`);
            return flights;

        } catch (error) {
            console.error('❌ MakeMyTrip scraping failed:', error.message);
            return [];
        }
    }

    deduplicateFlights(flights) {
        const seen = new Set();
        return flights.filter(flight => {
            const key = `${flight.airline}-${flight.departure.time}-${flight.arrival.time}-${flight.price}`;
            if (seen.has(key)) {
                return false;
            }
            seen.add(key);
            return true;
        });
    }

    getFallbackFlights(searchData) {
        console.log('🔄 Using fallback mock data');

        const originCode = this.getAirportCode(searchData.origin);
        const destCode = this.getAirportCode(searchData.destination);

        return [
            {
                airline: "IndiGo",
                flightNumber: "6E-123",
                departure: {
                    time: "06:30",
                    airport: originCode,
                    date: searchData.departureDate
                },
                arrival: {
                    time: "08:45",
                    airport: destCode,
                    date: searchData.departureDate
                },
                duration: "2h 15m",
                stops: 0,
                price: 4500,
                bookingUrl: "https://www.goindigo.in/",
                source: "Fallback"
            },
            {
                airline: "SpiceJet",
                flightNumber: "SG-456",
                departure: {
                    time: "14:20",
                    airport: originCode,
                    date: searchData.departureDate
                },
                arrival: {
                    time: "16:50",
                    airport: destCode,
                    date: searchData.departureDate
                },
                duration: "2h 30m",
                stops: 0,
                price: 3800,
                bookingUrl: "https://www.spicejet.com/",
                source: "Fallback"
            },
            {
                airline: "Air India",
                flightNumber: "AI-789",
                departure: {
                    time: "19:15",
                    airport: originCode,
                    date: searchData.departureDate
                },
                arrival: {
                    time: "21:35",
                    airport: destCode,
                    date: searchData.departureDate
                },
                duration: "2h 20m",
                stops: 0,
                price: 5200,
                bookingUrl: "https://www.airindia.in/",
                source: "Fallback"
            }
        ];
    }
}

module.exports = FlightScraper;
