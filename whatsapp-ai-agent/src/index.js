const express = require('express');
const { Client, LocalAuth } = require('whatsapp-web.js');
const qrcode = require('qrcode-terminal');
const { OpenAI } = require('openai');
require('dotenv').config();

// Import handlers (we'll create these next)
const ShoppingHandler = require('./handlers/shopping');
const AssignmentHandler = require('./handlers/assignment');
const YouTubeHandler = require('./handlers/youtube');
const FlightHandler = require('./handlers/flight');
const HotelHandler = require('./handlers/hotel');

// Initialize services
const app = express();
app.use(express.json());

// Debug: Check if API key is loaded
console.log('🔑 OpenAI API Key loaded:', process.env.OPENAI_API_KEY ? 'YES' : 'NO');
console.log('🔑 API Key starts with:', process.env.OPENAI_API_KEY ? process.env.OPENAI_API_KEY.substring(0, 10) + '...' : 'NOT FOUND');

const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY
});

// Initialize WhatsApp client
const whatsapp = new Client({
    authStrategy: new LocalAuth(),
    puppeteer: {
        headless: true,
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--single-process',
            '--disable-gpu'
        ]
    }
});

// Session management
const userSessions = new Map();

// Initialize handlers
const handlers = {
    shopping: new ShoppingHandler(openai, userSessions),
    assignment: new AssignmentHandler(openai),
    youtube: new YouTubeHandler(openai),
    flight: new FlightHandler(openai, userSessions),
    hotel: new HotelHandler(openai, userSessions)
};

// WhatsApp event handlers
whatsapp.on('qr', qr => {
    console.log('QR Code received, scan with WhatsApp:');
    qrcode.generate(qr, { small: true });
});

whatsapp.on('ready', () => {
    console.log('🎉 WhatsApp client is ready!');
    console.log('📱 Waiting for messages...');
    console.log('💡 Send "hello" to yourself to test!');
});

whatsapp.on('message', async message => {
    try {
        console.log(`\n🚨 ===== MESSAGE DETECTED ===== 🚨`);
        console.log(`📨 NEW MESSAGE RECEIVED!`);
        console.log(`From: ${message.from}`);
        console.log(`Body: ${message.body}`);
        console.log(`Type: ${message.type}`);
        console.log(`Has Media: ${message.hasMedia}`);
        console.log(`🚨 ========================== 🚨\n`);

        // Check if user has an active session first
        const userId = message.from;
        const existingSession = userSessions.get(userId);

        if (existingSession && existingSession.state !== 'idle') {
            console.log(`🔄 User has active session: ${existingSession.state}`);
            console.log(`📋 Session data:`, JSON.stringify(existingSession.data, null, 2));

            // Route to the appropriate handler based on session type
            switch (existingSession.type) {
                case 'flight':
                    await handlers.flight.handle(message);
                    return;
                case 'shopping':
                    await handlers.shopping.handle(message);
                    return;
                case 'hotel':
                    await handlers.hotel.handle(message);
                    return;
                default:
                    console.log(`⚠️ Unknown session type: ${existingSession.type}`);
                    break;
            }
        }

        // No active session, detect intent for new conversation
        const intent = await detectIntent(message.body);
        console.log(`🎯 Detected intent: ${intent.type}`);

        // Route to appropriate handler
        switch (intent.type) {
            case 'shopping':
                await handlers.shopping.handle(message);
                break;
            case 'youtube':
                await handlers.youtube.handle(message);
                break;
            case 'flight':
                await handlers.flight.handle(message);
                break;
            case 'hotel':
                await handlers.hotel.handle(message);
                break;
            default:
                if (message.hasMedia) {
                    await handlers.assignment.handle(message);
                } else {
                    await message.reply(
                        'Hi! I can help you with:\n\n' +
                        '🛍️ Shopping - "Find iPhone under $800"\n' +
                        '✈️ Flights - "Book flight NYC to London Dec 15"\n' +
                        '🏨 Hotels - "Find hotels in Paris"\n' +
                        '📚 Assignments - Send me a photo\n' +
                        '📺 YouTube - "Summarize [YouTube URL]"\n\n' +
                        'What would you like help with?'
                    );
                }
        }
    } catch (error) {
        console.error('Error processing message:', error);
        await message.reply('Sorry, I encountered an error. Please try again.');
    }
});

// Enhanced intent detection
async function detectIntent(text) {
    const lowercaseText = text.toLowerCase();

    console.log(`🔍 Intent Detection - Input: "${text}"`);
    console.log(`🔍 Lowercase: "${lowercaseText}"`);

    // Flight-related keywords - Enhanced detection
    const flightKeywords = [
        'flight', 'fly', 'book flight', 'plane', 'airline', 'airport',
        'travel to', 'trip to', 'book a flight', 'flight from', 'flight to'
    ];

    const hasFlightKeyword = flightKeywords.some(keyword => lowercaseText.includes(keyword));
    const hasBookKeyword = lowercaseText.includes('book');
    const hasCityPattern = /\b(mumbai|delhi|nyc|london|new york|los angeles|chicago|miami|boston|seattle|san francisco)\b/i.test(lowercaseText);

    if (hasFlightKeyword || (hasBookKeyword && hasCityPattern)) {
        console.log(`✈️ FLIGHT INTENT DETECTED!`);
        return { type: 'flight' };
    }

    // Shopping-related keywords
    if (lowercaseText.includes('buy') ||
        lowercaseText.includes('shop') ||
        lowercaseText.includes('order') ||
        (lowercaseText.includes('find') && (lowercaseText.includes('product') || lowercaseText.includes('price')))) {
        console.log(`🛍️ SHOPPING INTENT DETECTED!`);
        return { type: 'shopping' };
    }

    // YouTube-related keywords
    if (lowercaseText.includes('youtube') ||
        lowercaseText.includes('summarize') ||
        lowercaseText.includes('video')) {
        console.log(`📺 YOUTUBE INTENT DETECTED!`);
        return { type: 'youtube' };
    }

    // Hotel-related keywords
    if (lowercaseText.includes('hotel') ||
        lowercaseText.includes('stay') ||
        lowercaseText.includes('accommodation') ||
        lowercaseText.includes('booking')) {
        console.log(`🏨 HOTEL INTENT DETECTED!`);
        return { type: 'hotel' };
    }

    console.log(`❓ UNKNOWN INTENT - No keywords matched`);
    return { type: 'unknown' };
}

// Start Express server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
});

// Initialize WhatsApp
whatsapp.initialize();

// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('\n🛑 Shutting down gracefully...');

    // Clean up flight scraper resources
    if (handlers.flight) {
        await handlers.flight.cleanup();
    }

    // Close WhatsApp client
    if (whatsapp) {
        await whatsapp.destroy();
    }

    console.log('✅ Cleanup completed');
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('\n🛑 Received SIGTERM, shutting down gracefully...');

    // Clean up flight scraper resources
    if (handlers.flight) {
        await handlers.flight.cleanup();
    }

    // Close WhatsApp client
    if (whatsapp) {
        await whatsapp.destroy();
    }

    console.log('✅ Cleanup completed');
    process.exit(0);
});
